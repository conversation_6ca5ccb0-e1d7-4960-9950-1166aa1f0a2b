# QA Test Cases for EmbedPress Release - Latest Branch

## 1. Description Section

### What
This release includes critical bug fixes and improvements for EmbedPress:
- **Google Photos Carousel Fix**: Disabled redirects in Google Photos carousel layout to prevent unwanted navigation
- **PDF Flip-book Enhancement**: Fixed mouse wheel zoom issues in PDF flip-book viewer for better user interaction
- **CodeSandbox Embed Fix**: Resolved height/width display issues with CodeSandbox embeds
- **Google Photos UI Improvement**: Added functionality to remove round buttons from Google Photos embeds for cleaner display
- **CSS Optimizations**: Updated and optimized CSS files for better performance and styling

### Why
- **Improve User Experience**: Fix navigation issues and display problems that affect user interaction
- **Enhance PDF Viewing**: Ensure PDF flip-book functionality works smoothly with mouse wheel controls
- **Fix Embed Display Issues**: Resolve sizing problems with CodeSandbox embeds that affect content presentation
- **Clean UI Elements**: Remove unnecessary UI elements from Google Photos embeds for better visual experience
- **Performance Optimization**: Optimize CSS for better loading and rendering performance

### Impact
- **Google Photos Embeds**: Users will experience improved carousel navigation without unwanted redirects
- **PDF Flip-book Viewer**: Enhanced zoom functionality affects users viewing PDF documents
- **CodeSandbox Embeds**: Better sizing and display affects developers embedding CodeSandbox content
- **Google Photos Display**: Cleaner interface without round buttons affects visual presentation
- **Overall Performance**: CSS optimizations affect page loading and rendering across all embed types

## 2. QA Testing Checklist

### [ ] **Google Photos Carousel Layout** - Fixed redirect issues in carousel mode ❌ ✅
- **Routes to test**:
  - Post/page editor with Google Photos album URLs
  - Frontend pages with Google Photos carousel embeds
  - Gutenberg block editor with Google Photos content
- **Testing steps**:
  1. Create new post/page in WordPress admin
  2. Add Google Photos album URL that displays in carousel mode
  3. Verify carousel navigation works without redirecting to external sites
  4. Test left/right navigation arrows in carousel
  5. Check that users can browse photos without leaving the page
  6. Test on both frontend and backend preview
- **Edge cases**: Test with different album sizes, private/public albums, different photo formats

### [ ] **PDF Flip-book Mouse Wheel Zoom** - Enhanced zoom functionality ✅
- **Routes to test**:
  - Post/page editor with PDF document embeds
  - Frontend pages with PDF flip-book viewer
  - Elementor widgets with PDF documents
- **Testing steps**:
  1. Upload or embed a PDF document using EmbedPress
  2. Enable flip-book mode for the PDF
  3. Test mouse wheel zoom in and zoom out functionality
  4. Verify zoom works smoothly without conflicts
  5. Check zoom controls work in both directions
  6. Test zoom reset functionality
  7. Verify zoom works on different PDF page sizes
- **Edge cases**: Test with large PDFs, different zoom levels, touch devices, different browsers

### [ ] **CodeSandbox Height/Width Display** - Fixed sizing issues ✅
- **Routes to test**:
  - Post/page editor with CodeSandbox embed URLs
  - Frontend display of CodeSandbox embeds
  - Responsive design on different screen sizes
- **Testing steps**:
  1. Create new post/page
  2. Add CodeSandbox embed URL
  3. Verify embed displays with correct height and width
  4. Test responsive behavior on mobile and tablet
  5. Check that content is not cut off or oversized
  6. Verify iframe sizing matches container
  7. Test with different CodeSandbox project types
- **Edge cases**: Test with complex CodeSandbox projects, different aspect ratios, nested containers

### [ ] **Google Photos Round Button Removal** - UI improvement for cleaner display ❌ ✅
- **Routes to test**:
  - Google Photos embeds with the new remove-round-button.js script
  - Both same-origin and cross-origin iframe scenarios
  - Dynamic content loading scenarios
- **Testing steps**:
  1. Embed Google Photos content that normally shows round buttons
  2. Verify round buttons (.jx-svg-round-button) are hidden or removed
  3. Check that the removal script loads correctly
  4. Test with dynamically loaded Google Photos content
  5. Verify the script works with existing and new iframes
  6. Check console for any JavaScript errors
  7. Test mutation observer functionality for dynamic content
- **Edge cases**: Test with cross-origin iframes, slow-loading content, multiple Google Photos embeds on same page

### [ ] **CSS Optimizations** - Performance and styling improvements
- **Routes to test**:
  - All EmbedPress embed types and admin pages
  - Page loading performance
  - Visual styling consistency
- **Testing steps**:
  1. Test various embed types (YouTube, PDF, Google Photos, etc.)
  2. Check page loading speed and CSS optimization
  3. Verify styling consistency across different embed types
  4. Test responsive design on various screen sizes
  5. Check for any broken styles or layout issues
  6. Verify CSS minification and optimization
  7. Test with different WordPress themes
- **Edge cases**: Test with custom themes, RTL languages, high contrast modes

### [ ] **JavaScript Asset Loading** - New script integration
- **Routes to test**:
  - Pages with Google Photos embeds
  - WordPress admin and frontend
  - Script dependency management
- **Testing steps**:
  1. Check that remove-round-button.js loads correctly
  2. Verify script dependencies are properly managed
  3. Test script loading order and timing
  4. Check for JavaScript console errors
  5. Verify script works with WordPress caching plugins
  6. Test script performance impact
- **Edge cases**: Test with caching plugins, minification tools, CDN configurations

## Browser and Theme Compatibility Testing

### Browsers to Test
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)
- [ ] Mobile browsers (iOS Safari, Chrome Mobile)

### WordPress Themes to Test
- [ ] Default WordPress themes (Twenty Twenty-Four, Twenty Twenty-Three)
- [ ] Popular page builders (Elementor Hello, Astra)
- [ ] Custom themes with different CSS frameworks

### User Roles to Test
- [ ] Administrator (full access)
- [ ] Editor (content creation)
- [ ] Author (limited content access)
- [ ] Subscriber (view-only)

## Critical Path Testing Priority
1. **High Priority**: Google Photos carousel fix, PDF flip-book zoom fix, CodeSandbox sizing fix
2. **Medium Priority**: Round button removal, CSS optimizations
3. **Low Priority**: JavaScript asset loading optimization

## Notes
- Focus testing on Google Photos and PDF functionality as these are the main fixes
- Pay special attention to mouse wheel interactions in PDF flip-book mode
- Verify CodeSandbox embeds display correctly across different screen sizes
- Test Google Photos embeds for both visual improvements and functional fixes
- Check for any performance regressions with the new JavaScript file